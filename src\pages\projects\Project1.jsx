import React from "react";
import { useTheme } from "../../context/ThemeContext";
import {
  Github,
  ExternalLink,
  Monitor,
  Smartphone,
  Globe,
  MapPin,
  Languages,
  Eye,
  Users,
  BarChart3,
  Settings,
  Camera,
  MessageSquare,
  Shield,
  Database,
  Upload,
  FileText,
} from "lucide-react";
import { motion } from "framer-motion";
import laptop963 from "../../assets/laptop963.png";
import phone963 from "../../assets/phone963.png";
import dashboardScreen from "../../assets/dashboard-screen.png";

function Project1() {
  const { isDark } = useTheme();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const tourismFeatures = [
    {
      icon: <Globe className="w-5 h-5" />,
      text: "Interactive Tourism Map",
      color: "blue",
    },
    {
      icon: <Eye className="w-5 h-5" />,
      text: "360° Virtual Tours",
      color: "purple",
    },
    {
      icon: <Languages className="w-5 h-5" />,
      text: "Sign Language Support",
      color: "green",
    },
    {
      icon: <MapPin className="w-5 h-5" />,
      text: "Multi-language Platform",
      color: "orange",
    },
  ];

  const dashboardFeatures = [
    { 
      icon: <Database className="w-5 h-5" />, 
      text: "Cities & Regions Management",
      description: "Add, edit, and organize Syrian cities and regions"
    },
    { 
      icon: <MapPin className="w-5 h-5" />, 
      text: "Places & Attractions Control",
      description: "Manage tourist destinations, historical sites, and landmarks"
    },
    { 
      icon: <Languages className="w-5 h-5" />, 
      text: "Multi-Language Content",
      description: "Insert and manage content in Arabic, English, and other languages"
    },
    { 
      icon: <Camera className="w-5 h-5" />, 
      text: "Media Library Management",
      description: "Upload and organize images, videos, and 360° tour content"
    },
    { 
      icon: <FileText className="w-5 h-5" />, 
      text: "Categories & Services",
      description: "Create and manage tourism categories and available services"
    },
    { 
      icon: <Users className="w-5 h-5" />, 
      text: "Admin User Management",
      description: "Control admin access levels and user permissions"
    },
    { 
      icon: <BarChart3 className="w-5 h-5" />, 
      text: "Analytics Dashboard",
      description: "Track visitor statistics and engagement metrics"
    },
    { 
      icon: <MessageSquare className="w-5 h-5" />, 
      text: "Review & Content Moderation",
      description: "Manage user reviews and moderate platform content"
    },
  ];

  const techStack = [
    { name: "React", color: "bg-blue-500" },
    { name: "Node.js", color: "bg-green-500" },
    { name: "MongoDB", color: "bg-green-600" },
    { name: "Tailwind CSS", color: "bg-cyan-500" },
    { name: "Three.js", color: "bg-purple-500" },
    { name: "i18n", color: "bg-orange-500" },
  ];

  return (
    <div className="w-full">
      {/* First Section - Tourism Website */}
      <motion.section
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12"
      >
        <div className="max-w-7xl mx-auto w-full">
          {/* Hero Section */}
          <motion.div
            variants={itemVariants}
            className={`relative overflow-hidden rounded-3xl ${
              isDark
                ? "bg-gradient-to-br from-gray-800 to-gray-900"
                : "bg-gradient-to-br from-white to-gray-50"
            } shadow-2xl border ${isDark ? "border-gray-700" : "border-gray-200"}`}
          >
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary-light"></div>
              <div
                className="absolute inset-0"
                style={{
                  backgroundImage: `radial-gradient(circle at 25% 25%, ${
                    isDark ? "#374151" : "#f3f4f6"
                  } 2px, transparent 2px)`,
                  backgroundSize: "24px 24px",
                }}
              ></div>
            </div>

            {/* Header */}
            <div className="relative p-8 lg:p-12">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div className="flex-1">
                  <motion.div
                    variants={itemVariants}
                    className="flex items-center gap-3 mb-4"
                  >
                    <div className="w-12 h-12 bg-gradient-to-r from-primary to-primary-light rounded-xl flex items-center justify-center shadow-lg">
                      <Globe className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h1
                        className={`text-3xl lg:text-4xl font-bold ${
                          isDark ? "text-white" : "text-gray-900"
                        }`}
                      >
                        963sy Tourism Platform
                      </h1>
                      <p
                        className={`text-sm ${
                          isDark ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        Discover Syria's Heritage
                      </p>
                    </div>
                  </motion.div>

                  <motion.p
                    variants={itemVariants}
                    className={`text-lg leading-relaxed max-w-2xl ${
                      isDark ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    A comprehensive digital platform showcasing Syria's rich
                    historical, religious, and cultural heritage. Features immersive
                    virtual tours, accessibility-focused design with sign language
                    support, and an interactive map connecting visitors to Syria's
                    most treasured destinations.
                  </motion.p>
                </div>

                {/* Action Buttons */}
                <motion.div
                  variants={itemVariants}
                  className="flex flex-col sm:flex-row gap-4 lg:flex-col xl:flex-row xl:gap-3"
                >
                  <a
                    href="https://963sy-demo.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group flex items-center justify-center gap-3 px-6 py-3 bg-gradient-to-r from-primary to-primary-light text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
                  >
                    <ExternalLink className="w-5 h-5 group-hover:rotate-12 transition-transform" />
                    <span>Live Demo</span>
                  </a>
                  <a
                    href="https://github.com/yourusername/963sy"
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`group flex items-center justify-center gap-3 px-6 py-3 rounded-xl font-semibold border-2 transition-all duration-300 ${
                      isDark
                        ? "border-gray-600 text-gray-300 hover:border-gray-500 hover:bg-gray-800"
                        : "border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50"
                    }`}
                  >
                    <Github className="w-5 h-5 group-hover:scale-110 transition-transform" />
                    <span>Source Code</span>
                  </a>
                </motion.div>
              </div>
            </div>

            {/* Tech Stack */}
            <motion.div variants={itemVariants} className="px-8 lg:px-12 pb-8">
              <div className="flex flex-wrap gap-3">
                {techStack.map((tech, index) => (
                  <motion.span
                    key={index}
                    whileHover={{ scale: 1.05 }}
                    className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium text-white shadow-md ${tech.color}`}
                  >
                    <div className="w-2 h-2 bg-white rounded-full opacity-80"></div>
                    {tech.name}
                  </motion.span>
                ))}
              </div>
            </motion.div>
          </motion.div>

          {/* Content Grid */}
          <div className="grid lg:grid-cols-2 gap-8 mt-12">
            {/* Left Column - Tourism Features */}
            <motion.div
              variants={itemVariants}
              className={`p-6 rounded-2xl ${
                isDark
                  ? "bg-gray-800 border-gray-700"
                  : "bg-white border-gray-200"
              } border shadow-lg`}
            >
              <div className="flex items-center gap-3 mb-6">
                <Monitor
                  className={`w-6 h-6 ${
                    isDark ? "text-primary-light" : "text-primary"
                  }`}
                />
                <h3
                  className={`text-xl font-bold ${
                    isDark ? "text-white" : "text-gray-900"
                  }`}
                >
                  Tourism Platform Features
                </h3>
              </div>

              <div className="grid sm:grid-cols-2 gap-4">
                {tourismFeatures.map((feature, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.02 }}
                    className={`p-4 rounded-xl border transition-all duration-300 ${
                      isDark
                        ? "bg-gray-700 border-gray-600 hover:border-gray-500"
                        : "bg-gray-50 border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <div
                      className={`inline-flex items-center justify-center w-10 h-10 rounded-lg mb-3 ${
                        feature.color === "blue"
                          ? "bg-blue-100 text-blue-600"
                          : feature.color === "purple"
                          ? "bg-purple-100 text-purple-600"
                          : feature.color === "green"
                          ? "bg-green-100 text-green-600"
                          : "bg-orange-100 text-orange-600"
                      }`}
                    >
                      {feature.icon}
                    </div>
                    <p
                      className={`font-medium ${
                        isDark ? "text-gray-200" : "text-gray-800"
                      }`}
                    >
                      {feature.text}
                    </p>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Right Column - Device Mockups */}
            <motion.div variants={itemVariants} className="relative">
              <div className="relative h-80 sm:h-96 lg:h-[400px]">
                {/* Laptop Mockup */}
                <motion.div
                  initial={{ x: -50, opacity: 0 }}
                  whileInView={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="absolute top-0 left-0 w-4/5 h-3/4 z-10"
                >
                  <div className="relative w-full h-full">
                    <img
                      src={laptop963}
                      alt="963sy tourism website on laptop"
                      className="w-full h-full object-contain drop-shadow-2xl"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary-light/20 rounded-lg blur-xl -z-10"></div>
                  </div>
                </motion.div>

                {/* Phone Mockup */}
                <motion.div
                  initial={{ x: 50, opacity: 0 }}
                  whileInView={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="absolute bottom-0 right-0 w-2/5 h-3/5 z-20"
                >
                  <div className="relative w-full h-full">
                    <img
                      src={phone963}
                      alt="963sy tourism website on phone"
                      className="w-full h-full object-contain drop-shadow-xl"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg blur-lg -z-10"></div>
                  </div>
                </motion.div>

                {/* Floating elements */}
                <div className="hidden sm:block absolute top-1/4 right-1/4 w-3 h-3 bg-primary rounded-full animate-pulse opacity-60"></div>
                <div className="hidden sm:block absolute bottom-1/3 left-1/4 w-2 h-2 bg-primary-light rounded-full animate-bounce opacity-40"></div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Second Section - Admin Dashboard */}
      <motion.section
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12"
      >
        <div className="max-w-7xl mx-auto w-full">
          {/* Dashboard Header */}
          <motion.div
            variants={itemVariants}
            className={`relative overflow-hidden rounded-3xl ${
              isDark
                ? "bg-gradient-to-br from-gray-800 to-gray-900"
                : "bg-gradient-to-br from-white to-gray-50"
            } shadow-2xl border ${isDark ? "border-gray-700" : "border-gray-200"} mb-12`}
          >
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-blue-500"></div>
              <div
                className="absolute inset-0"
                style={{
                  backgroundImage: `radial-gradient(circle at 75% 25%, ${
                    isDark ? "#374151" : "#f3f4f6"
                  } 2px, transparent 2px)`,
                  backgroundSize: "24px 24px",
                }}
              ></div>
            </div>

            <div className="relative p-8 lg:p-12">
              <motion.div
                variants={itemVariants}
                className="flex items-center gap-3 mb-4"
              >
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2
                    className={`text-3xl lg:text-4xl font-bold ${
                      isDark ? "text-white" : "text-gray-900"
                    }`}
                  >
                    Admin Dashboard
                  </h2>
                  <p
                    className={`text-sm ${
                      isDark ? "text-gray-400" : "text-gray-600"
                    }`}
                  >
                    Complete Content Management System
                  </p>
                </div>
              </motion.div>

              <motion.p
                variants={itemVariants}
                className={`text-lg leading-relaxed max-w-3xl ${
                  isDark ? "text-gray-300" : "text-gray-700"
                }`}
              >
                A powerful administrative interface that provides complete control over the tourism platform. 
                Manage cities, attractions, content in multiple languages, media files, user permissions, 
                and track detailed analytics - all from a centralized, intuitive dashboard.
              </p>
            </div>
          </motion.div>

          {/* Dashboard Content Grid */}
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Left Column - Dashboard Features */}
            <motion.div
              variants={itemVariants}
              className={`p-6 rounded-2xl ${
                isDark
                  ? "bg-gray-800 border-gray-700"
                  : "bg-white border-gray-200"
              } border shadow-lg`}
            >
              <div className="flex items-center gap-3 mb-6">
                <Settings
                  className={`w-6 h-6 ${
                    isDark ? "text-green-400" : "text-green-600"
                  }`}
                />
                <h3
                  className={`text-xl font-bold ${
                    isDark ? "text-white" : "text-gray-900"
                  }`}
                >
                  Dashboard Capabilities
                </h3>
              </div>

              <div className="space-y-4">
                {dashboardFeatures.map((feature, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.01 }}
                    className={`p-4 rounded-xl border transition-all duration-300 ${
                      isDark
                        ? "bg-gray-700 border-gray-600 hover:border-gray-500"
                        : "bg-gray-50 border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div
                        className={`p-2 rounded-lg flex-shrink-0 ${
                          isDark
                            ? "bg-gray-600 text-green-400"
                            : "bg-green-100 text-green-600"
                        }`}
                      >
                        {feature.icon}
                      </div>
                      <div>
                        <h4
                          className={`font-semibold mb-1 ${
                            isDark ? "text-gray-200" : "text-gray-800"
                          }`}
                        >
                          {feature.text}
                        </h4>
                        <p
                          className={`text-sm ${
                            isDark ? "text-gray-400" : "text-gray-600"
                          }`}
                        >
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Right Column - Dashboard Mockup */}
            <motion.div variants={itemVariants} className="relative">
              <div className="relative h-[600px]">
                <motion.div
                  initial={{ scale: 0.9, opacity: 0 }}
                  whileInView={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="w-full h-full"
                >
                  <div className="relative w-full h-full">
                    <img
                      src={dashboardScreen}
                      alt="963sy admin dashboard interface"
                      className="w-full h-full object-contain drop-shadow-2xl rounded-lg"
                    />
                    {/* Glow effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-lg blur-xl -z-10"></div>
                  </div>
                </motion.div>

                {/* Floating dashboard elements */}
                <div className="hidden lg:block absolute -top-4 -right-4 w-4 h-4 bg-green-500 rounded-full animate-pulse opacity-60"></div>
                <div className="hidden lg:block absolute -bottom-4 -left-4 w-3 h-3 bg-blue-500 rounded-full animate-bounce opacity-40"></div>
                <div className="hidden lg:block absolute top-1/3 -right-2 w-2 h-2 bg-purple-500 rounded-full animate-ping opacity-50"></div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>
    </div>
  );
}

export default Project1;