@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --bg-color: white;
  --text-color: black;
  --primary: #1a90ff;
  --transition-duration: 0.3s;
  --transition-timing: ease-in-out;
}

.dark {
  --bg-color: #121212;
  --text-color: #ffffff;
  --primary: #3aa3ff;
}
html {
  scroll-behavior: smooth;
}
/* Smooth transitions for theme changes */
body, .bg-white, .bg-black-200, .text-gray-900, .text-white {
  transition: 
    background-color var(--transition-duration) var(--transition-timing),
    color var(--transition-duration) var(--transition-timing);
}

/* Optional: Set body background to match theme */
body {
  background-color: var(--bg-color);
  color: var(--text-color);
}