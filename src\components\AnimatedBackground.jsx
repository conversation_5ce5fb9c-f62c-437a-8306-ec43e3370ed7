import React, { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";

const AnimatedBackground = ({ className = "" }) => {
  const [circles, setCircles] = useState([]);
  const containerRef = useRef(null);
  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });
  const animationRef = useRef(null);
  
  useEffect(() => {
    // Generate random circles
    const generateCircles = () => {
      const newCircles = [];
      const colors = [
        "rgba(26, 144, 255, 0.5)",   // Primary blue
        "rgba(99, 182, 255, 0.6)",   // Primary light blue
        "rgba(0, 93, 180, 0.4)",     // Primary dark blue
        "rgba(212, 167, 255, 0.6)",  // Vibrant purple
        "rgba(255, 214, 228, 0.6)",  // Soft pink
        "rgba(151, 71, 255, 0.5)",   // Deep purple
        "rgba(178, 157, 255, 0.6)",  // Lavender
        "rgba(255, 179, 179, 0.5)",  // Light coral
      ];
      
      for (let i = 0; i < 20; i++) {
        newCircles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: 80 + Math.random() * 300,
          color: colors[Math.floor(Math.random() * colors.length)],
          duration: 5 + Math.random() * 15, // Faster duration
          delay: Math.random() * 3, // Shorter delay
          baseX: Math.random() * 60 - 30,
          baseY: Math.random() * 60 - 30,
          moveFactor: 0.8 + Math.random() * 2,
          rotationSpeed: (Math.random() - 0.5) * 0.2, // Faster rotation
          pulseSpeed: 1 + Math.random() * 3, // Faster pulse
        });
      }
      setCircles(newCircles);
    };
    
    generateCircles();
    
    // Automatic animation
    const autoAnimate = () => {
      const time = Date.now() * 0.002; // Faster time multiplier
      
      // Automatic movement using sine/cosine
      const x = Math.sin(time * 0.5) * 0.5 + 0.5; // Faster frequency
      const y = Math.cos(time * 0.4) * 0.5 + 0.5; // Faster frequency
      
      setMousePosition({ x, y });
      animationRef.current = requestAnimationFrame(autoAnimate);
    };
    
    autoAnimate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);
  
  return (
    <div 
      ref={containerRef}
      className={`absolute inset-0 overflow-hidden ${className}`}
    >
      {circles.map((circle) => (
        <motion.div
          key={circle.id}
          className="absolute rounded-full blur-2xl"
          style={{
            backgroundColor: circle.color,
            width: circle.size,
            height: circle.size,
            left: `${circle.x}%`,
            top: `${circle.y}%`,
            willChange: "transform, opacity",
          }}
          initial={{ scale: 0.8, opacity: 0, rotate: 0 }}
          animate={{ 
            scale: [
              0.8, 
              1.0 + 0.3 * Math.sin(circle.pulseSpeed), 
              0.9, 
              1.1, 
              0.95
            ],
            opacity: [0, 0.9, 0.7, 0.8, 0.6],
            rotate: [0, 360 * circle.rotationSpeed],
            x: [
              circle.baseX, 
              circle.baseX + mousePosition.x * 80 * circle.moveFactor * (circle.id % 3 - 1),
              circle.baseX - mousePosition.x * 60 * circle.moveFactor * (circle.id % 3 - 1),
              circle.baseX + mousePosition.x * 40 * circle.moveFactor * (circle.id % 3 - 1),
              circle.baseX - mousePosition.x * 70 * circle.moveFactor * (circle.id % 3 - 1),
            ],
            y: [
              circle.baseY,
              circle.baseY - mousePosition.y * 80 * circle.moveFactor * (circle.id % 2 - 0.5),
              circle.baseY + mousePosition.y * 60 * circle.moveFactor * (circle.id % 2 - 0.5),
              circle.baseY - mousePosition.y * 40 * circle.moveFactor * (circle.id % 2 - 0.5),
              circle.baseY + mousePosition.y * 70 * circle.moveFactor * (circle.id % 2 - 0.5),
            ]
          }}
          transition={{
            duration: circle.duration,
            delay: circle.delay,
            repeat: Infinity,
            repeatType: "reverse",
            ease: [0.25, 0.1, 0.25, 1],
          }}
        />
      ))}
      <div className="absolute z-0 inset-0 bg-white/10 backdrop-blur-[1px]" />
    </div>
  );
};

export default AnimatedBackground;


